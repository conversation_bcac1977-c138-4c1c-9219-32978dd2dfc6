import React from 'react';
import {View, Text, ViewStyle} from 'react-native';

import {theme} from '../../constants';
import {ProductType} from '../../types';
import useCurrency from '../../catalog/hooks/use-currency';

type Props = {
  item: ProductType;
  version: 1 | 2;
  containerStyle?: ViewStyle;
  numberOfLines?: number;
};

const ProductPrice: React.FC<Props> = ({
  item,
  containerStyle,
  numberOfLines = 1,
}): JSX.Element => {
  const currency = useCurrency();
  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        ...containerStyle,
      }}
    >
      {item.old_price && (
        <Text
          style={{
            marginRight: 4,
            textDecorationLine: 'line-through',
            ...theme.fonts.DMSans_400Regular,
            fontSize: 12,
            color: theme.colors.textColor,
            lineHeight: 12 * 1.5,
          }}
        >
          ${item.old_price.toFixed(2)}
        </Text>
      )}
      <Text
        style={{
          ...theme.fonts.DMSans_500Medium,
          fontSize: 14,
          lineHeight: 14 * 1.5,
          color: theme.colors.mainColor,
        }}
      >
        {currency.currency} {item.price.toFixed(2)}
      </Text>
    </View>
  );
};

export default ProductPrice;
