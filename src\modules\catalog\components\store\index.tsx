import {View, Text, StyleSheet} from 'react-native';
import {useProductsFilteringStore} from '../../store/products-filter';
import ProductContainer from '../products/product/container/default';
import {theme} from '../../../constants';
import useProductsFilteringStoreSubmission from '../../hooks/products/use-products-filtering-store-submission';
import CategoryPageProductsWrapper from './pages/category-page-products-wrapper';
import FilterPageProductsWrapper from './pages/filter-page-products-wrapper';
import PaginationMangement from '../../../components/pagination/pagination-management';

interface Props {
  brandSlug?: string;
  categoriesSlugs?: string[];
}

export default function Store({brandSlug, categoriesSlugs = []}: Props) {
  const {setJoinedPageData, setJoinedPageParam, joinedPageData} =
    useProductsFilteringStore((store) => store);

  const {
    products,
    pagination,
    isLoading,
    page,
    setPage,
    pagesNumber,
    paginatedListRef: scrollViewPortRef,
  } = useProductsFilteringStoreSubmission({
    limit: 20,
    paginationInUrlIsUsed: true,
  });

  // useEffect(() => {
  //   setJoinedPageParam({
  //     categorySlug: categoriesSlugs[0] ? categoriesSlugs[0] : null,
  //     subCategorySlug: categoriesSlugs[1] ? categoriesSlugs[1] : null,
  //     subSubCategorySlug: categoriesSlugs[2] ? categoriesSlugs[2] : null,
  //     brandSlug: brandSlug ? brandSlug : null,
  //   });

  //   return () => {
  //     setJoinedPageData({
  //       category: null,
  //       brand: null,
  //     });

  //     setJoinedPageParam({
  //       categorySlug: null,
  //       subCategorySlug: null,
  //       subSubCategorySlug: null,
  //       brandSlug: null,
  //     });
  //   };
  // }, [pathname]);

  const FilterWrapper =
    categoriesSlugs.length > 0
      ? CategoryPageProductsWrapper
      : FilterPageProductsWrapper;

  return (
    <>
      <FilterWrapper
        categorySlug={categoriesSlugs[categoriesSlugs.length - 1]}
        productCount={pagination?.records}
      >
        <View style={styles.container}>
          {!isLoading ? (
            products && products.length > 0 ? (
              <View style={styles.productsGrid}>
                {products.map((product, idx) => (
                  <ProductContainer key={idx} product={product} />
                ))}
              </View>
            ) : (
              <View style={styles.noProductsContainer}>
                <Text style={styles.noProductsText}>No Products</Text>
              </View>
            )
          ) : (
            <View style={styles.productsGrid}>
              {Array.from({length: 20}).map((_, idx) => (
                <ProductContainer key={idx} product={null} />
              ))}
            </View>
          )}
        </View>
      </FilterWrapper>
      <div className='flex justify-center mt-4'>
        <PaginationMangement
          currentPage={page}
          pagesNumber={pagesNumber}
          changePage={setPage}
        />
      </div>

      {joinedPageData.category &&
      joinedPageData.category.description &&
      joinedPageData.category.description !== '<p></p>' ? (
        <div className='w-full mt-5 bg-primary-muted L:py-4 L:px-5 py-2 px-3 flex flex-col L:space-y-5 space-y-3 line-clamp-3'>
          <Text>Category Details</Text>
          {/* <ExpandableText
            text={joinedPageData.category.description as string}
          /> */}
        </div>
      ) : (
        joinedPageData.brand &&
        joinedPageData.brand.description &&
        joinedPageData.brand.description !== '<p></p>' && (
          <div className='w-full mt-5 bg-primary-muted L:py-4 L:px-5 py-2 px-3 flex flex-col L:space-y-5 space-y-3 line-clamp-3'>
            <Text>Brand Details</Text>
            {/* <ExpandableText text={joinedPageData.brand.description as string} /> */}
          </div>
        )
      )}
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  productsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 8,
  },
  noProductsContainer: {
    minHeight: 450,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  noProductsText: {
    fontSize: 32,
    textAlign: 'center',
    color: theme.colors.textColor,
    ...theme.fonts.DMSans_500Medium,
  },
});
