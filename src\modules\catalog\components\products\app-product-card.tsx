import React from 'react';
import {ProductType} from '../../types/products';
import {components} from '../../../../components';
import {adaptCatalogProductToApp} from '../../utils/type-adapters/product-adapter';

interface Props {
  product: ProductType;
  version?: number;
  lastItem?: boolean;
}

const AppProductCard: React.FC<Props> = ({
  product,
  version = 2,
  lastItem = false,
}): JSX.Element => {
  const adaptedProduct = adaptCatalogProductToApp(product);

  return (
    <components.ProductCard
      item={adaptedProduct}
      version={version}
      lastItem={lastItem}
    />
  );
};

export default AppProductCard;
