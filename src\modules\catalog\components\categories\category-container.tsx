import {View, Text, Image, TouchableOpacity, StyleSheet} from 'react-native';
import {LinearGradient} from 'expo-linear-gradient';
import {CategoryType} from '../../types/categories';
import {theme} from '../../../constants';

interface Props {
  category: CategoryType;
  onPress?: () => void;
}

export default function CategoryContainer({category, onPress}: Props) {
  if (!category) return null;

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <LinearGradient
        colors={['#4F46E5', theme.colors.mainColor]}
        style={styles.imageContainer}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 1}}
      >
        {category.image ? (
          <Image
            source={{uri: category.image}}
            style={styles.categoryImage}
            resizeMode='contain'
          />
        ) : (
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>AKAL</Text>
          </View>
        )}
      </LinearGradient>
      <Text style={styles.categoryName} numberOfLines={1}>
        {category.name}
      </Text>
      <Text style={styles.productCount}>
        {category.numberOfProducts || 0} Produits
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'center',
    gap: 8,
  },
  imageContainer: {
    width: '100%',
    aspectRatio: 1,
    borderRadius: 16,
    padding: 8,
    justifyContent: 'flex-end',
    alignItems: 'center',
    overflow: 'hidden',
  },
  categoryImage: {
    width: 120,
    height: 120,
  },
  logoContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    ...theme.fonts.DMSans_700Bold,
  },
  categoryName: {
    textAlign: 'center',
    color: theme.colors.mainColor,
    fontSize: 14,
    fontWeight: '500',
    ...theme.fonts.DMSans_500Medium,
  },
  productCount: {
    textAlign: 'center',
    color: '#6B7280',
    fontSize: 12,
    ...theme.fonts.DMSans_400Regular,
  },
});
