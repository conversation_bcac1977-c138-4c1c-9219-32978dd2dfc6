import {ProductType} from '../../types/products';
import {ProductType as AppProductType} from '../../../../types/ProductType';

export function adaptCatalogProductToApp(
  catalogProduct: ProductType,
): AppProductType {
  const firstItem = catalogProduct.items[0];
  const firstPrice = firstItem?.prices[0];
  const colors = firstItem?.variations?.map((v) => v.value) || [];
  const sizes = firstItem?.variations?.map((v) => v.value) || [];

  return {
    id: parseInt(catalogProduct.id) || 0,
    name: catalogProduct.name,
    price: firstPrice?.promotionalPrice || firstPrice?.realPrice || 0,
    rating: 4.5,
    image: firstItem?.image || '',
    images: firstItem?.images?.join(',') || '',
    sizes: sizes,
    size: sizes[0] || '',
    colors: colors,
    color: colors[0] || '',
    description: catalogProduct.description || '',
    categories: catalogProduct.brand?.name || '',
    is_bestseller: false,
    is_featured: false,
    is_out_of_stock: !firstItem?.inStock,
    old_price:
      firstPrice && firstPrice.promotionalPrice < firstPrice.realPrice
        ? firstPrice.realPrice
        : undefined,
    quantity: 1,
    reviews: [],
    types: [],
  };
}

export function adaptCatalogProductsToApp(
  catalogProducts: ProductType[],
): AppProductType[] {
  return catalogProducts.map(adaptCatalogProductToApp);
}
