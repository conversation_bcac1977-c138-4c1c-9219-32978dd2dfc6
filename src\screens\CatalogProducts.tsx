import React, {useState} from 'react';
import {View, StyleSheet, SafeAreaView, StatusBar} from 'react-native';
import {useAppDispatch} from '../hooks';

// App imports
import {theme} from '../constants';
import {components} from '../components';
import ProductsList from '../catalog/components/products/products-list';

import {useProductsFilteringStore} from '../catalog/store/products-filter';

const CatalogProductsScreen: React.FC = (): JSX.Element => {
  const dispatch = useAppDispatch();
  const [localSearch, setLocalSearch] = useState('');

  // Catalog store state
  const {
    selectedBrands,
    categories,
    priceRange,
    criteria,
    search,
    filterVersion,
    setSearch,
  } = useProductsFilteringStore();

  // Get selected category slugs
  const getSelectedCategorySlugs = () => {
    const selectedCategories: string[] = [];
    const extractSlugs = (cats: any[]) => {
      cats.forEach((cat) => {
        if (cat.selected) {
          selectedCategories.push(cat.slug);
        }
        if (cat.subCategories?.length > 0) {
          extractSlugs(cat.subCategories);
        }
      });
    };
    extractSlugs(categories);
    return selectedCategories;
  };

  // Handle search
  const handleSearchSubmit = () => {
    setSearch(localSearch);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle='dark-content'
        backgroundColor={theme.colors.white}
        translucent={false}
      />

      {/* Header */}
      <components.Header
        title='Products'
        goBack={true}
        basket={true}
        search={true}
      />

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <components.InputField
          placeholder='Search products...'
          value={localSearch}
          onChangeText={setLocalSearch}
          containerStyle={styles.searchInput}
          keyboardType='default'
        />
        <components.Button
          title='Search'
          onPress={handleSearchSubmit}
          containerStyle={styles.searchButton}
        />
      </View>

      {/* Products List */}
      <View style={styles.content}>
        <ProductsList type={'default'} />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    alignItems: 'center',
    gap: 10,
  },
  searchInput: {
    flex: 1,
    marginRight: 0,
  },
  searchButton: {
    width: 80,
    height: 50,
  },
  content: {
    flex: 1,
  },
});

export default CatalogProductsScreen;
