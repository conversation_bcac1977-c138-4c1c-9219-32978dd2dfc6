import React, {Dispatch, SetStateAction} from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import {theme} from '../../../constants';
import FilterOptions from './filter-options';

interface Props {
  filterIsOpen: boolean;
  setFilterIsOpen: Dispatch<SetStateAction<boolean>>;
  isLoading: boolean;
}

const {width} = Dimensions.get('window');

export default function FilterDrawer({
  setFilterIsOpen,
  filterIsOpen,
  isLoading = false,
}: Props) {
  const doubleExtraL = 900;

  return width < doubleExtraL ? (
    <Modal
      visible={filterIsOpen}
      animationType='slide'
      presentationStyle='pageSheet'
      onRequestClose={() => setFilterIsOpen(false)}
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Filtres</Text>
          <TouchableOpacity
            onPress={() => setFilterIsOpen(false)}
            style={styles.closeButton}
          >
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>
        </View>
        <FilterOptions
          setFilterIsOpen={setFilterIsOpen}
          isLoading={isLoading}
          filterHeaderIsUsed={true}
        />
      </SafeAreaView>
    </Modal>
  ) : (
    <FilterOptions
      setFilterIsOpen={setFilterIsOpen}
      isLoading={isLoading}
      criteriaDropDownIsUsed={false}
    />
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.mainColor,
    ...theme.fonts.DMSans_700Bold,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 20,
    color: '#6B7280',
    fontWeight: 'bold',
  },
});
