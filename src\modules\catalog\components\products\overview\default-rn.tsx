import React, {useState} from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';

import {theme} from '../../../../constants';
import ProductContainer from '../product/container/default';
import {BrandType} from '../../../types/brands';
import useProducts from '../../../hooks/products/use-products';
import {CategoryType} from '../../../types/categories';
import {CriteriaType, ProductsSectionsVariant} from '../../../types';
import useCategories from '../../../hooks/categories/use-categories';

interface Props {
  maxProductsNumber?: number;
  variant?: ProductsSectionsVariant;
  similarProductSlug?: string;
  similarProductName?: string;
  category?: CategoryType;
  brand?: BrandType;
}

const ProductsOverview: React.FC<Props> = ({
  maxProductsNumber,
  variant = 'default',
  similarProductSlug,
  similarProductName,
  category,
  brand,
}): JSX.Element => {
  const [selectedCategory, setSelectedCategory] = useState<CategoryType | null>(
    null,
  );
  const {categories} = useCategories();

  function getCriteriaBasedOnProductsVariant(
    variant: ProductsSectionsVariant,
  ): CriteriaType {
    switch (variant) {
      case 'news':
        return 'createdAtDesc';
      case 'similarProducts':
        return 'displayOrder';
      case 'recommended':
        return 'displayOrder';
      case 'mostSold':
        return 'mostSold';
    }

    return 'displayOrder';
  }

  const {products, productsAreLoading} = useProducts({
    limit: 7,
    criteria: getCriteriaBasedOnProductsVariant(
      variant === 'selection' ? 'default' : variant,
    ),
    similarProductSlug: similarProductSlug,
    categoriesSlugs:
      (variant === 'default' || variant === 'mostSold') && selectedCategory
        ? selectedCategory.subCategories.length === 0
          ? [selectedCategory.slug]
          : [
              selectedCategory.slug,
              ...selectedCategory.subCategories
                .flatMap((cat) =>
                  cat.subCategories.length > 0 ? cat.subCategories : [cat],
                )
                .map((cat) => cat.slug),
            ]
        : undefined,
  });

  const getTitle = () => {
    if (similarProductName) {
      return `Similar to ${similarProductName}`;
    }

    switch (variant) {
      case 'mostSold':
        return 'Best Sellers';
      case 'selection':
        return 'Our Selection';
      default:
        return 'Featured Products';
    }
  };

  const handleDiscoverMorePress = () => {
    console.log('Navigate to more products');
  };

  const renderCategorySelector = () => {
    if (!categories || categories.length === 0 || variant !== 'default') {
      return null;
    }

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categorySelectorContent}
        style={styles.categorySelector}
      >
        <TouchableOpacity
          style={[
            styles.categoryButton,
            !selectedCategory && styles.categoryButtonActive,
          ]}
          onPress={() => setSelectedCategory(null)}
        >
          <Text
            style={[
              styles.categoryButtonText,
              !selectedCategory && styles.categoryButtonTextActive,
            ]}
          >
            All
          </Text>
        </TouchableOpacity>

        {categories.slice(0, 6).map((category: any) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryButton,
              selectedCategory?.id === category.id &&
                styles.categoryButtonActive,
            ]}
            onPress={() => setSelectedCategory(category)}
          >
            <Text
              style={[
                styles.categoryButtonText,
                selectedCategory?.id === category.id &&
                  styles.categoryButtonTextActive,
              ]}
            >
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  const renderProductsGrid = () => {
    return (
      <View style={styles.gridContainer}>
        {products?.slice(0, 4).map((product) => (
          <View key={product.id} style={styles.productContainer}>
            <ProductContainer product={product} />
          </View>
        ))}
      </View>
    );
  };

  const renderProductsCarousel = () => {
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.carouselContent}
        style={styles.carousel}
      >
        {products?.map((product, index) => (
          <View
            key={product.id}
            style={[
              styles.carouselItem,
              {marginRight: index === products.length - 1 ? 20 : 10},
            ]}
          >
            <ProductContainer product={product} />
          </View>
        ))}
      </ScrollView>
    );
  };

  const renderDiscoverButton = () => {
    return (
      <TouchableOpacity
        style={styles.discoverButton}
        onPress={handleDiscoverMorePress}
        activeOpacity={0.8}
      >
        <Text style={styles.discoverButtonText}>Discover More</Text>
      </TouchableOpacity>
    );
  };

  if (productsAreLoading) {
    return <ProductsOverviewSkeletons />;
  }

  if (!products || products.length === 0) {
    return <View />;
  }

  const shouldUseCarousel = products.length > 4;

  return (
    <View style={styles.container}>
      {/* Title */}
      <View style={styles.titleContainer}>
        <Text style={styles.title}>{getTitle()}</Text>
      </View>

      {/* Category Selector */}
      {renderCategorySelector()}

      {/* Products Display */}
      {shouldUseCarousel ? renderProductsCarousel() : renderProductsGrid()}

      {/* Discover More Button */}
      {renderDiscoverButton()}
    </View>
  );
};

// Skeleton loader component
export const ProductsOverviewSkeletons: React.FC<{className?: string}> = () => {
  return (
    <View style={styles.skeletonContainer}>
      <View style={styles.skeletonTitle} />
      <View style={styles.gridContainer}>
        {Array.from({length: 4}).map((_, idx) => (
          <View key={idx} style={styles.skeletonProduct} />
        ))}
      </View>
      <View style={styles.skeletonButton} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'center',
  },
  titleContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    ...theme.fonts.DMSans_700Bold,
    fontSize: 20,
    color: theme.colors.textColor,
    textAlign: 'center',
  },
  categorySelector: {
    width: '100%',
    marginBottom: 24,
  },
  categorySelectorContent: {
    paddingHorizontal: 20,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
    marginRight: 12,
  },
  categoryButtonActive: {
    backgroundColor: theme.colors.mainColor,
  },
  categoryButtonText: {
    ...theme.fonts.DMSans_500Medium,
    fontSize: 14,
    color: '#666',
  },
  categoryButtonTextActive: {
    color: theme.colors.white,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 24,
    paddingHorizontal: 16,
    gap: 8,
  },
  productContainer: {
    width: '47%',
    marginBottom: 12,
  },
  carousel: {
    width: '100%',
    marginBottom: 24,
  },
  carouselContent: {
    paddingLeft: 16,
    paddingRight: 16,
  },
  carouselItem: {
    width: 160,
    marginRight: 12,
  },
  discoverButton: {
    backgroundColor: theme.colors.mainColor,
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  discoverButtonText: {
    ...theme.fonts.DMSans_700Bold,
    fontSize: 14,
    color: theme.colors.white,
    textTransform: 'uppercase',
  },
  // Skeleton styles
  skeletonContainer: {
    width: '100%',
    alignItems: 'center',
  },
  skeletonTitle: {
    width: 200,
    height: 20,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    marginBottom: 24,
  },
  skeletonProduct: {
    width: '48%',
    height: 200,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    marginBottom: 10,
  },
  skeletonButton: {
    width: 160,
    height: 40,
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    marginTop: 24,
  },
});

export default ProductsOverview;
