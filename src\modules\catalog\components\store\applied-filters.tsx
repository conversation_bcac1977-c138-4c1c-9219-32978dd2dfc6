import {useEffect, useState} from 'react';
import {useProductsFilteringStore} from '../../store/products-filter';
import {AppliedFilter} from './applied-filter';
import {BrandType} from '../../types/brands';
import {CategorySelectionType} from '../../types/categories';
import {ScrollView, View, StyleSheet} from 'react-native';
import {components} from '../../../components';

export default function AppliedFilters() {
  const [displayedSubCategories, setDisplayedSubCategories] = useState<
    CategorySelectionType[]
  >([]);
  const [displayedcategories, setDisplayedcategories] = useState<
    CategorySelectionType[]
  >([]);
  const [displayedBrands, setDisplayedBrands] = useState<BrandType[]>([]);

  const {
    search,
    joinedPageData,
    selectedBrands,
    categories,
    clearAll: clearAllFilter,
    applyFilter,
  } = useProductsFilteringStore();

  useEffect(() => {
    setDisplayedBrands(selectedBrands);
    setDisplayedcategories(categories.filter((cat) => cat.selected));
    setDisplayedSubCategories(
      categories.flatMap((cat) =>
        cat.subCategories.filter((subCat) => subCat.selected),
      ),
    );

    return () => {
      setDisplayedBrands([]);
      setDisplayedSubCategories([]);
      setDisplayedcategories([]);
    };
  }, [categories, selectedBrands]);

  const clearAll = () => {
    clearAllFilter();
    applyFilter();
  };
  const searchAppliedFilterIsDisplayed = search !== '';
  const categoriesAppliedFilterIsDisplayed = displayedcategories.length !== 0;
  const subCategoriesAppliedFilterIsDisplayed =
    displayedSubCategories.length !== 0;
  const brandsAppliedFilterIsDisplayed =
    !joinedPageData.brand && displayedBrands.length !== 0;

  const clearButtonIsDisplayed =
    searchAppliedFilterIsDisplayed ||
    categoriesAppliedFilterIsDisplayed ||
    subCategoriesAppliedFilterIsDisplayed ||
    brandsAppliedFilterIsDisplayed;

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {searchAppliedFilterIsDisplayed && (
          <AppliedFilter type={'search'} item={{id: '', name: search}} />
        )}
        {categoriesAppliedFilterIsDisplayed &&
          displayedcategories.map((category, idx) => (
            <AppliedFilter key={idx} type={'category'} item={category} />
          ))}
        {subCategoriesAppliedFilterIsDisplayed &&
          displayedSubCategories.map((subCategory, idx) => (
            <AppliedFilter key={idx} type={'subCategory'} item={subCategory} />
          ))}
        {brandsAppliedFilterIsDisplayed &&
          displayedBrands.map((brand, idx) => (
            <AppliedFilter key={idx} type={'brand'} item={brand} />
          ))}
        {clearButtonIsDisplayed && (
          <components.Button
            title='Clear All'
            onPress={clearAll}
            containerStyle={styles.clearButton}
            transparent={true}
          />
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  scrollContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 16,
  },
  clearButton: {
    marginLeft: 8,
    minWidth: 80,
  },
});
