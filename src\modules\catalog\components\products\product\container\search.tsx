import React, {useEffect, useState} from 'react';
import {Text, Image, StyleSheet} from 'react-native';
import {ProductType} from '../../../../types/products';
import useCurrency from '../../../../hooks/use-currency';
import {formatPrice} from '../../../../utils/prices-transformation';
import useProductUrl from '../../../../hooks/products/use-product-url';
import Button from '../../../../../../components/buttons/Button';

interface Props {
  product: ProductType | null;
  onClick?: () => void;
}

export default function SearchProductContainer({product, onClick}: Props) {
  const productPageUrl = useProductUrl(product);
  const [productImage, setProductImage] = useState(
    'https://via.placeholder.com/300x300/f0f0f0/cccccc?text=No+Image',
  );

  const {currency} = useCurrency();

  const availablePromotion =
    product &&
    product.items[0].prices[0].promotionalPrice !==
      product.items[0].prices[0].realPrice;

  useEffect(() => {
    if (product && product.items.length > 0)
      setProductImage(product.items[0].image);
  }, [product]);

  const handleQuickAdd = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  return (
    product && (
      <div className='w-full flex flex-col rounded-md shadow-sm overflow-hidden group h-full hover:shadow-md transition-shadow'>
        <a
          href={productPageUrl}
          onClick={onClick}
          className='w-full flex flex-col h-full'
        >
          {/* Product Image with Heart Icon */}
          <div className='relative w-full pt-[100%] bg-white'>
            <Image
              source={{uri: productImage}}
              style={styles.image}
              resizeMode='cover'
              onError={() =>
                setProductImage(
                  'https://via.placeholder.com/300x300/f0f0f0/cccccc?text=No+Image',
                )
              }
            />
            {/* <HeartButton
              size='sm'
              className='absolute top-2 right-2 S:top-3 S:right-3'
              onClick={(e: any) => {
                e.preventDefault();
                e.stopPropagation();
                handleWishlistToggle();
              }}
            /> */}
          </div>

          {/* Product Info */}
          <div className='flex flex-col p-2 S:p-3 M:p-4 flex-grow'>
            {/* Vendor - Static content, not served from backend */}
            {/* <div className="flex items-center mb-2">
            <Text
              textStyle="TS7"
              className="text-gray-500 flex items-center text-xs"
            >
              <span className="text-gray-600">{t("vendor")}</span>
              <span className="mx-1 text-gray-400">•</span>
              <span className="flex items-center">
                <span className="text-yellow-500 mr-1">★</span>
                <span className="text-gray-600">0.00</span>
              </span>
              <span className="text-gray-400 ml-1">{t("noReviews")}</span>
            </Text>
          </div> */}

            {/* Product Name */}
            <Text>{product.name}</Text>

            {/* Price - Pushed to bottom */}
            <div className='flex items-center mt-auto'>
              <Text>
                {`${formatPrice(
                  product.items[0]?.prices[0]?.promotionalPrice,
                )} ${currency}`}
              </Text>
              {availablePromotion && (
                <Text>
                  {`${formatPrice(
                    product.items[0].prices[0].realPrice,
                  )} ${currency}`}
                </Text>
              )}
            </div>
          </div>
        </a>

        <div className='px-2 S:px-3 M:px-4 pb-2 S:pb-3 M:pb-4 pt-1'>
          <Button title='  Quick Add' onPress={() => handleQuickAdd} />
        </div>
      </div>
    )
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    width: '100%',
    height: 200, // or any height you want
  },
  image: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  },
});
